<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f02cb052-86e6-4d06-88cb-d7651e5a8fce" name="更改" comment="refactor(worker): 优化性能和代码结构&#10;&#10;对 Telegram Bot Worker 进行全面重构，提升性能和代码可维护性。&#10;&#10;关键变更点：&#10;1. 优化错误处理：增加统一的 try-catch 机制和智能重试逻辑&#10;2. 重构验证流程：简化验证码处理逻辑，提取通用函数处理过期验证&#10;3. 优化数据库操作：使用原子操作和缓存机制减少数据库查询次数&#10;4. 改进消息发送：增强错误处理和重试机制，提高消息可靠性&#10;5. 代码结构调整：提取公共函数，减少代码重复，提高可读性&#10;6. 性能优化：实现智能缓存清理机制，避免内存泄漏">
      <change beforePath="$PROJECT_DIR$/_worker.js" beforeDir="false" afterPath="$PROJECT_DIR$/_worker.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30BAzd8Dp4PStShk0TbpHgCXUcY" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/code/codeEnv&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\code\codeProject\my-github\ctt" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-WS-251.26094.131" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f02cb052-86e6-4d06-88cb-d7651e5a8fce" name="更改" comment="" />
      <created>1753090855922</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753090855922</updated>
      <workItem from="1753090857077" duration="8308000" />
      <workItem from="1754299619594" duration="2285000" />
    </task>
    <task id="LOCAL-00001" summary="11">
      <option name="closed" value="true" />
      <created>1753091264280</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753091264280</updated>
    </task>
    <task id="LOCAL-00002" summary="11">
      <option name="closed" value="true" />
      <created>1753092936877</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753092936877</updated>
    </task>
    <task id="LOCAL-00003" summary="11">
      <option name="closed" value="true" />
      <created>1753093806983</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753093806983</updated>
    </task>
    <task id="LOCAL-00004" summary="11">
      <option name="closed" value="true" />
      <created>1753096744103</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753096744103</updated>
    </task>
    <task id="LOCAL-00005" summary="11">
      <option name="closed" value="true" />
      <created>1754300190793</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754300190793</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="refactor(worker): 优化性能和代码结构&#10;&#10;对 Telegram Bot Worker 进行全面重构，提升性能和代码可维护性。&#10;&#10;关键变更点：&#10;1. 优化错误处理：增加统一的 try-catch 机制和智能重试逻辑&#10;2. 重构验证流程：简化验证码处理逻辑，提取通用函数处理过期验证&#10;3. 优化数据库操作：使用原子操作和缓存机制减少数据库查询次数&#10;4. 改进消息发送：增强错误处理和重试机制，提高消息可靠性&#10;5. 代码结构调整：提取公共函数，减少代码重复，提高可读性&#10;6. 性能优化：实现智能缓存清理机制，避免内存泄漏" />
    <MESSAGE value="refactor(worker): 优化性能和代码结构&#10;&#10;对 Telegram Bot Worker 进行全面重构，提升性能和代码可维护性。&#10;&#10;关键变更点：&#10;1. 优化错误处理：增加统一的 try-catch 机制和智能重试逻辑&#10;2. 重构验证流程：简化验证码处理逻辑，提取通用函数处理过期验证&#10;3. 优化数据库操作：使用原子操作和缓存机制减少数据库查询次数&#10;4. 改进消息发送：增强错误处理和重试机制，提高消息可靠性&#10;5. 代码结构调整：提取公共函数，减少代码重复，提高可读性&#10;6. 性能优化：实现智能缓存清理机制，避免内存泄漏" />
    <MESSAGE value="refactor(worker): 移除GitHub相关功能并优化时间处理&#10;&#10;清理项目中的非核心功能，简化依赖结构，并优化时间格式化处理。&#10;&#10;关键变更点：&#10;1. 移除GitHub相关功能：删除管理面板中的GitHub项目链接和相关提示信息&#10;2. 清理构建脚本：移除star-chart生成脚本及相关依赖包&#10;3. 优化时间处理：新增时区感知的时间格式化函数，支持上海时区&#10;4. 改进代码一致性：统一缓存清理方法，使用delete替代undefined赋值&#10;5. 简化依赖管理：移除Chart.js相关的多个依赖包，减少项目体积&#10;6. 代码格式优化：统一SQL查询语句的缩进格式" />
    <MESSAGE value="refactor(worker): 移除GitHub相关功能并优化时间处理&#10;&#10;清理项目中的非核心功能，简化依赖结构，并优化时间格式化处理。&#10;&#10;关键变更点：&#10;1. 移除GitHub相关功能：删除管理面板中的GitHub项目链接和相关提示信息&#10;2. 清理构建脚本：移除star-chart生成脚本及相关依赖包&#10;3. 优化时间处理：新增时区感知的时间格式化函数，支持上海时区&#10;4. 改进代码一致性：统一缓存清理方法，使用delete替代undefined赋值&#10;5. 简化依赖管理：移除Chart.js相关的多个依赖包，减少项目体积&#10;6. 代码格式优化：统一SQL查询语句的缩进格式" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor(worker): 移除GitHub相关功能并优化时间处理&#10;&#10;清理项目中的非核心功能，简化依赖结构，并优化时间格式化处理。&#10;&#10;关键变更点：&#10;1. 移除GitHub相关功能：删除管理面板中的GitHub项目链接和相关提示信息&#10;2. 清理构建脚本：移除star-chart生成脚本及相关依赖包&#10;3. 优化时间处理：新增时区感知的时间格式化函数，支持上海时区&#10;4. 改进代码一致性：统一缓存清理方法，使用delete替代undefined赋值&#10;5. 简化依赖管理：移除Chart.js相关的多个依赖包，减少项目体积&#10;6. 代码格式优化：统一SQL查询语句的缩进格式" />
  </component>
</project>